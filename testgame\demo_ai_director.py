#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统演示脚本
展示AI导演功能的完整工作流程
"""

import requests
import json
import time

def demo_ai_director():
    """演示AI导演功能"""
    base_url = "http://localhost:4001"
    
    print("🎭 AI导演系统功能演示")
    print("=" * 50)
    
    # 1. 检查系统状态
    print("\n1. 检查AI导演系统状态...")
    try:
        response = requests.get(f"{base_url}/api/ai-director/status/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 系统状态: {data.get('status', '未知')}")
        else:
            print(f"   ❌ 状态检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 2. 演示故事大纲解析
    print("\n2. 演示故事大纲解析...")
    story_outline = {
        "outline_text": """
        《逆天改命》
        主题：凡人逆天修仙，挑战命运束缚
        核心冲突：废灵根修士林逸风与天道的对抗
        主要角色：
        - 林逸风：主角，废灵根却有惊人悟性
        - 苏清雪：青云宗天才弟子，后成为道侣
        - 魔君血煞：最终反派，代表天道意志
        
        剧情要点：
        1. 序章：林逸风觉醒特殊体质
        2. 起承：拜入青云宗，遇到苏清雪
        3. 高潮：各方势力争夺逆天秘密
        4. 转合：与天道直接对抗
        5. 终章：改变修仙界格局
        """
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/ai-director/parse-outline/",
            json=story_outline,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ 解析成功!")
                print(f"   📖 故事标题: {data.get('title', '未知')}")
                print(f"   🎯 主题: {data.get('theme', '未知')}")
                print(f"   ⚔️ 核心冲突: {data.get('main_conflict', '未知')}")
                print(f"   👥 关键角色: {', '.join(data.get('key_characters', []))}")
            else:
                print(f"   ❌ 解析失败: {data.get('error', '未知错误')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 解析异常: {e}")
    
    # 3. 演示AI决策生成
    print("\n3. 演示AI决策生成...")
    
    test_events = [
        {
            "event_type": "CultivationBreakthrough",
            "character": "林逸风",
            "description": "主角成功突破到筑基期，天地灵气汇聚",
            "location": "青云峰后山",
            "priority": "HIGH"
        },
        {
            "event_type": "CharacterMeeting", 
            "character": "苏清雪",
            "description": "苏清雪在藏书阁遇到林逸风",
            "location": "青云宗藏书阁",
            "priority": "MEDIUM"
        },
        {
            "event_type": "ConflictEscalation",
            "character": "魔君血煞",
            "description": "魔君血煞感知到逆天之力的觉醒",
            "location": "魔域深渊",
            "priority": "HIGH"
        }
    ]
    
    for i, event in enumerate(test_events, 1):
        print(f"\n   事件 {i}: {event['event_type']}")
        try:
            response = requests.post(
                f"{base_url}/api/ai-director/make-decision/",
                json=event,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"   ✅ 决策生成成功!")
                    print(f"   🎭 决策类型: {data.get('decision_type', '未知')}")
                    print(f"   📝 决策内容: {data.get('content', '无内容')[:100]}...")
                    print(f"   ⏱️ 响应时间: {data.get('response_time', 0):.1f}ms")
                    print(f"   🎯 置信度: {data.get('confidence', 0):.2f}")
                else:
                    print(f"   ❌ 决策失败: {data.get('error', '未知错误')}")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 决策异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print("\n🎉 AI导演系统演示完成!")
    print("\n💡 提示:")
    print("   - 所有功能都在模拟模式下运行")
    print("   - 配置真实API密钥可获得更智能的响应")
    print("   - 系统支持实时剧情生成和角色互动")

if __name__ == "__main__":
    demo_ai_director()
