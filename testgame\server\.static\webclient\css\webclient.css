/* ---

  Style sheet for Evennia's web client.

  This should possibly somehow be incoorporated with the
  overall website theme in the future?

--- */

/* Overall element look */
html, body {
    height: 100%;
    width: 100%;
}
body {
    background: #000;
    color: #ccc;
    font-size: .9em;
    font-family: 'DejaVu Sans Mono', Consolas, Inconsolata, '<PERSON><PERSON>sole', monospace;
    line-height: 1.4em;
    overflow: hidden;
}
@media screen and (max-width: 480px) {
  body {
    font-size: .5rem;
    line-height: .7rem;
  }
}

a:link, a:visited { color: inherit; }
a:hover, a:active { color: inherit;
                    font-weight: bold;}

/* Set this to e.g. bolder if wanting to have ansi-highlights bolden
 * stand-alone text.*/
strong {font-weight: bold;}

div {margin:0px;}

.hidden { display: none; }

/* Utility messages (green) */
.sys { color: #0f0 }

/* Messages echoed back after input */
.inp { color: #555 }

/* Messages returned from the server (most messages) */
.out {
  color: #aaa;
  background-color: #000;
  white-space: pre-wrap;
}

/* Error messages (red) */
.err { color: #f00; }

/* Prompt base (white) */
.prompt {color: #fff }

.underline { text-decoration: underline; }

/* Inverse - this will make a dotted outline. is there a more
 * ANSI-similar way? */
.inverse {
    outline-style: dotted;
    outline-color: invert;
}

/* Create blinking text */
.blink {
    animation: blink-animation 1s steps(5, start) infinite;
    -webkit-animation: blink-animation 1s steps(5, start) infinite;
}
@keyframes blink-animation {
  to {
    visibility: hidden;
  }
}
@-webkit-keyframes blink-animation {
  to {
    visibility: hidden;
  }
}

/* Style specific classes corresponding to formatted, narative text. */
.wrapper {
  height: 100%;
}

.card {
  background-color: #333;
}

.tabspace {
  white-space: pre;
  tab-size: 4;
  -moz-tab-size: 4;
}

/* Container surrounding entire client */
#clientwrapper {
  height: 100%;
}

/* Main scrolling message area */

#messagewindow {
    overflow-y: auto;
    overflow-x: hidden;
    overflow-wrap: break-word;
}

#messagewindow {
    overflow-y: auto;
    overflow-x: hidden;
    overflow-wrap: break-word;
}

/* Input field */
#input {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
}

#inputfield, #inputsizer {
    height: 100%;
    background: #000;
    color: #fff;
    padding: 0 .45rem;
    font-size: 1.1rem;
    font-family: 'DejaVu Sans Mono', Consolas, Inconsolata, 'Lucida Console', monospace;
    resize: none;
}
#inputsend {
  height: 100%;
}
#inputcontrol {
  height: 100%;
}

#inputfield:focus {
}

/* prompt area above input field */
.prompt {
  max-height: 3rem;
}

#splitbutton {
    width: 2rem;
    font-size: 2rem;
    color: #a6a6a6;
    background-color: transparent;
    border: 0px;
}

#splitbutton:hover {
    color: white;
    cursor: pointer;
}

#panebutton {
    width: 2rem;
    font-size: 2rem;
    color: #a6a6a6;
    background-color: transparent;
    border: 0px;
}

#panebutton:hover {
    color: white;
    cursor: pointer;
}

#undobutton {
    width: 2rem;
    font-size: 2rem;
    color: #a6a6a6;
    background-color: transparent;
    border: 0px;
}

#undobutton:hover {
    color: white;
    cursor: pointer;
}

.button {
    width: fit-content;
    padding: 1em;
    color: black;
    border: 1px solid black;
    background-color: darkgray;
    margin: 0 auto;
}

.splitbutton:hover {
    cursor: pointer;
}

#optionsbutton {
    width: 2rem;
    font-size: 2rem;
    color: #a6a6a6;
    background-color: transparent;
    border: 0px;
}

#optionsbutton:hover {
    color: white;
    cursor: pointer;
}

#toolbar {
    position: fixed;
    top: .5rem;
    right: .5rem;
    z-index: 1;
}

/* No javascript warning */
#connecting {
    position: absolute;
    width: 100%;
    padding: .5em .9em
}

/* Dialog window */
.dialog {
    display: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -75%);
    position: absolute;
    z-index: 10;
    background-color: #fefefe;
    border: 1px solid #888;
    color: lightgray;
    background-color: #2c2c2c;

}

#optionsdialog {
    width: 50%;
}

#helpdialog {
    width: 725px;
    height: 65%;
}

#helpdialog .dialogcontent {
    background-color: #1e1e1e;
    color: #ccc;
}

.dialogcontentparent {
    height: calc(100% - 44px - 40px);
}

.dialogcontent {
    overflow: auto;
    height: 100%;
    padding: 20px;
}

.dialogtitle {
    border-bottom: 1px solid #888;
    padding: 10px 20px;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    cursor: move;
    font-weight: bold;
    font-size: 16px;
    color: white;
    background-color: #595959;
}

.dialogclose {
    color: #d5d5d5;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.dialogclose:hover, .dialogclose:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}
.gutter.gutter-vertical {
    cursor: row-resize;
    background-image:  url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFAQMAAABo7865AAAABlBMVEVHcEzMzMzyAv2sAAAAAXRSTlMAQObYZgAAABBJREFUeF5jOAMEEAIEEFwAn3kMwcB6I2AAAAAASUVORK5CYII=')
}

.gutter.gutter-horizontal {
    cursor: col-resize;
    background-image:  url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==')
}

.split {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;

    overflow-y: auto;
    overflow-x: hidden;
}

.split-sub {
    padding: .5rem;
}

.content {
    border: 1px solid #C0C0C0;
    box-shadow: inset 0 1px 2px #e4e4e4;
    background-color: black;
    padding: 1rem;
}
@media screen and (max-width: 480px) {
  .content {
    padding: .5rem;
  }
}

.gutter {
    background-color: grey;

    background-repeat: no-repeat;
    background-position: 50%;
}

.split.split-horizontal, .gutter.gutter-horizontal {
    height: 100%;
    float: left;
}


/* MXP links */
#mxplink {
  text-decoration: underline;
}


/* XTERM256 colors */

.color-000 { color: #000000; }
.color-001 { color: #800000; }
.color-002 { color: #008000; }
.color-003 { color: #808000; }
.color-004 { color: #000080; }
.color-005 { color: #800080; }
.color-006 { color: #008080; }
.color-007 { color: #c0c0c0; }
.color-008 { color: #808080; }
.color-009 { color: #ff0000; }
.color-010 { color: #00ff00; }
.color-011 { color: #ffff00; }
.color-012 { color: #0000ff; }
.color-013 { color: #ff00ff; }
.color-014 { color: #00ffff; }
.color-015 { color: #ffffff; }
.color-016 { color: #000000; }
.color-017 { color: #00005f; }
.color-018 { color: #000087; }
.color-019 { color: #0000af; }
.color-020 { color: #0000df; }
.color-021 { color: #0000ff; }
.color-022 { color: #005f00; }
.color-023 { color: #005f5f; }
.color-024 { color: #005f87; }
.color-025 { color: #005faf; }
.color-026 { color: #005fdf; }
.color-027 { color: #005fff; }
.color-028 { color: #008700; }
.color-029 { color: #00875f; }
.color-030 { color: #008787; }
.color-031 { color: #0087af; }
.color-032 { color: #0087df; }
.color-033 { color: #0087ff; }
.color-034 { color: #00af00; }
.color-035 { color: #00af5f; }
.color-036 { color: #00af87; }
.color-037 { color: #00afaf; }
.color-038 { color: #00afdf; }
.color-039 { color: #00afff; }
.color-040 { color: #00df00; }
.color-041 { color: #00df5f; }
.color-042 { color: #00df87; }
.color-043 { color: #00dfaf; }
.color-044 { color: #00dfdf; }
.color-045 { color: #00dfff; }
.color-046 { color: #00ff00; }
.color-047 { color: #00ff5f; }
.color-048 { color: #00ff87; }
.color-049 { color: #00ffaf; }
.color-050 { color: #00ffdf; }
.color-051 { color: #00ffff; }
.color-052 { color: #5f0000; }
.color-053 { color: #5f005f; }
.color-054 { color: #5f0087; }
.color-055 { color: #5f00af; }
.color-056 { color: #5f00df; }
.color-057 { color: #5f00ff; }
.color-058 { color: #5f5f00; }
.color-059 { color: #5f5f5f; }
.color-060 { color: #5f5f87; }
.color-061 { color: #5f5faf; }
.color-062 { color: #5f5fdf; }
.color-063 { color: #5f5fff; }
.color-064 { color: #5f8700; }
.color-065 { color: #5f875f; }
.color-066 { color: #5f8787; }
.color-067 { color: #5f87af; }
.color-068 { color: #5f87df; }
.color-069 { color: #5f87ff; }
.color-070 { color: #5faf00; }
.color-071 { color: #5faf5f; }
.color-072 { color: #5faf87; }
.color-073 { color: #5fafaf; }
.color-074 { color: #5fafdf; }
.color-075 { color: #5fafff; }
.color-076 { color: #5fdf00; }
.color-077 { color: #5fdf5f; }
.color-078 { color: #5fdf87; }
.color-079 { color: #5fdfaf; }
.color-080 { color: #5fdfdf; }
.color-081 { color: #5fdfff; }
.color-082 { color: #5fff00; }
.color-083 { color: #5fff5f; }
.color-084 { color: #5fff87; }
.color-085 { color: #5fffaf; }
.color-086 { color: #5fffdf; }
.color-087 { color: #5fffff; }
.color-088 { color: #870000; }
.color-089 { color: #87005f; }
.color-090 { color: #870087; }
.color-091 { color: #8700af; }
.color-092 { color: #8700df; }
.color-093 { color: #8700ff; }
.color-094 { color: #875f00; }
.color-095 { color: #875f5f; }
.color-096 { color: #875f87; }
.color-097 { color: #875faf; }
.color-098 { color: #875fdf; }
.color-099 { color: #875fff; }
.color-100 { color: #878700; }
.color-101 { color: #87875f; }
.color-102 { color: #878787; }
.color-103 { color: #8787af; }
.color-104 { color: #8787df; }
.color-105 { color: #8787ff; }
.color-106 { color: #87af00; }
.color-107 { color: #87af5f; }
.color-108 { color: #87af87; }
.color-109 { color: #87afaf; }
.color-110 { color: #87afdf; }
.color-111 { color: #87afff; }
.color-112 { color: #87df00; }
.color-113 { color: #87df5f; }
.color-114 { color: #87df87; }
.color-115 { color: #87dfaf; }
.color-116 { color: #87dfdf; }
.color-117 { color: #87dfff; }
.color-118 { color: #87ff00; }
.color-119 { color: #87ff5f; }
.color-120 { color: #87ff87; }
.color-121 { color: #87ffaf; }
.color-122 { color: #87ffdf; }
.color-123 { color: #87ffff; }
.color-124 { color: #af0000; }
.color-125 { color: #af005f; }
.color-126 { color: #af0087; }
.color-127 { color: #af00af; }
.color-128 { color: #af00df; }
.color-129 { color: #af00ff; }
.color-130 { color: #af5f00; }
.color-131 { color: #af5f5f; }
.color-132 { color: #af5f87; }
.color-133 { color: #af5faf; }
.color-134 { color: #af5fdf; }
.color-135 { color: #af5fff; }
.color-136 { color: #af8700; }
.color-137 { color: #af875f; }
.color-138 { color: #af8787; }
.color-139 { color: #af87af; }
.color-140 { color: #af87df; }
.color-141 { color: #af87ff; }
.color-142 { color: #afaf00; }
.color-143 { color: #afaf5f; }
.color-144 { color: #afaf87; }
.color-145 { color: #afafaf; }
.color-146 { color: #afafdf; }
.color-147 { color: #afafff; }
.color-148 { color: #afdf00; }
.color-149 { color: #afdf5f; }
.color-150 { color: #afdf87; }
.color-151 { color: #afdfaf; }
.color-152 { color: #afdfdf; }
.color-153 { color: #afdfff; }
.color-154 { color: #afff00; }
.color-155 { color: #afff5f; }
.color-156 { color: #afff87; }
.color-157 { color: #afffaf; }
.color-158 { color: #afffdf; }
.color-159 { color: #afffff; }
.color-160 { color: #df0000; }
.color-161 { color: #df005f; }
.color-162 { color: #df0087; }
.color-163 { color: #df00af; }
.color-164 { color: #df00df; }
.color-165 { color: #df00ff; }
.color-166 { color: #df5f00; }
.color-167 { color: #df5f5f; }
.color-168 { color: #df5f87; }
.color-169 { color: #df5faf; }
.color-170 { color: #df5fdf; }
.color-171 { color: #df5fff; }
.color-172 { color: #df8700; }
.color-173 { color: #df875f; }
.color-174 { color: #df8787; }
.color-175 { color: #df87af; }
.color-176 { color: #df87df; }
.color-177 { color: #df87ff; }
.color-178 { color: #dfaf00; }
.color-179 { color: #dfaf5f; }
.color-180 { color: #dfaf87; }
.color-181 { color: #dfafaf; }
.color-182 { color: #dfafdf; }
.color-183 { color: #dfafff; }
.color-184 { color: #dfdf00; }
.color-185 { color: #dfdf5f; }
.color-186 { color: #dfdf87; }
.color-187 { color: #dfdfaf; }
.color-188 { color: #dfdfdf; }
.color-189 { color: #dfdfff; }
.color-190 { color: #dfff00; }
.color-191 { color: #dfff5f; }
.color-192 { color: #dfff87; }
.color-193 { color: #dfffaf; }
.color-194 { color: #dfffdf; }
.color-195 { color: #dfffff; }
.color-196 { color: #ff0000; }
.color-197 { color: #ff005f; }
.color-198 { color: #ff0087; }
.color-199 { color: #ff00af; }
.color-200 { color: #ff00df; }
.color-201 { color: #ff00ff; }
.color-202 { color: #ff5f00; }
.color-203 { color: #ff5f5f; }
.color-204 { color: #ff5f87; }
.color-205 { color: #ff5faf; }
.color-206 { color: #ff5fdf; }
.color-207 { color: #ff5fff; }
.color-208 { color: #ff8700; }
.color-209 { color: #ff875f; }
.color-210 { color: #ff8787; }
.color-211 { color: #ff87af; }
.color-212 { color: #ff87df; }
.color-213 { color: #ff87ff; }
.color-214 { color: #ffaf00; }
.color-215 { color: #ffaf5f; }
.color-216 { color: #ffaf87; }
.color-217 { color: #ffafaf; }
.color-218 { color: #ffafdf; }
.color-219 { color: #ffafff; }
.color-220 { color: #ffdf00; }
.color-221 { color: #ffdf5f; }
.color-222 { color: #ffdf87; }
.color-223 { color: #ffdfaf; }
.color-224 { color: #ffdfdf; }
.color-225 { color: #ffdfff; }
.color-226 { color: #ffff00; }
.color-227 { color: #ffff5f; }
.color-228 { color: #ffff87; }
.color-229 { color: #ffffaf; }
.color-230 { color: #ffffdf; }
.color-231 { color: #ffffff; }
.color-232 { color: #080808; }
.color-233 { color: #121212; }
.color-234 { color: #1c1c1c; }
.color-235 { color: #262626; }
.color-236 { color: #303030; }
.color-237 { color: #3a3a3a; }
.color-238 { color: #444444; }
.color-239 { color: #4e4e4e; }
.color-240 { color: #585858; }
.color-241 { color: #606060; }
.color-242 { color: #666666; }
.color-243 { color: #767676; }
.color-244 { color: #808080; }
.color-245 { color: #8a8a8a; }
.color-246 { color: #949494; }
.color-247 { color: #9e9e9e; }
.color-248 { color: #a8a8a8; }
.color-249 { color: #b2b2b2; }
.color-250 { color: #bcbcbc; }
.color-251 { color: #c6c6c6; }
.color-252 { color: #d0d0d0; }
.color-253 { color: #dadada; }
.color-254 { color: #e4e4e4; }
.color-255 { color: #eeeeee; }
.bgcolor-000 { background-color: #000000; }
.bgcolor-001 { background-color: #800000; }
.bgcolor-002 { background-color: #008000; }
.bgcolor-003 { background-color: #808000; }
.bgcolor-004 { background-color: #000080; }
.bgcolor-005 { background-color: #800080; }
.bgcolor-006 { background-color: #008080; }
.bgcolor-007 { background-color: #c0c0c0; }
.bgcolor-008 { background-color: #808080; }
.bgcolor-009 { background-color: #ff0000; }
.bgcolor-010 { background-color: #00ff00; }
.bgcolor-011 { background-color: #ffff00; }
.bgcolor-012 { background-color: #0000ff; }
.bgcolor-013 { background-color: #ff00ff; }
.bgcolor-014 { background-color: #00ffff; }
.bgcolor-015 { background-color: #ffffff; }
.bgcolor-016 { background-color: #000000; }
.bgcolor-017 { background-color: #00005f; }
.bgcolor-018 { background-color: #000087; }
.bgcolor-019 { background-color: #0000af; }
.bgcolor-020 { background-color: #0000df; }
.bgcolor-021 { background-color: #0000ff; }
.bgcolor-022 { background-color: #005f00; }
.bgcolor-023 { background-color: #005f5f; }
.bgcolor-024 { background-color: #005f87; }
.bgcolor-025 { background-color: #005faf; }
.bgcolor-026 { background-color: #005fdf; }
.bgcolor-027 { background-color: #005fff; }
.bgcolor-028 { background-color: #008700; }
.bgcolor-029 { background-color: #00875f; }
.bgcolor-030 { background-color: #008787; }
.bgcolor-031 { background-color: #0087af; }
.bgcolor-032 { background-color: #0087df; }
.bgcolor-033 { background-color: #0087ff; }
.bgcolor-034 { background-color: #00af00; }
.bgcolor-035 { background-color: #00af5f; }
.bgcolor-036 { background-color: #00af87; }
.bgcolor-037 { background-color: #00afaf; }
.bgcolor-038 { background-color: #00afdf; }
.bgcolor-039 { background-color: #00afff; }
.bgcolor-040 { background-color: #00df00; }
.bgcolor-041 { background-color: #00df5f; }
.bgcolor-042 { background-color: #00df87; }
.bgcolor-043 { background-color: #00dfaf; }
.bgcolor-044 { background-color: #00dfdf; }
.bgcolor-000 { background-color: #000000; }
.bgcolor-001 { background-color: #800000; }
.bgcolor-002 { background-color: #008000; }
.bgcolor-003 { background-color: #808000; }
.bgcolor-004 { background-color: #000080; }
.bgcolor-005 { background-color: #800080; }
.bgcolor-006 { background-color: #008080; }
.bgcolor-007 { background-color: #c0c0c0; }
.bgcolor-008 { background-color: #808080; }
.bgcolor-009 { background-color: #ff0000; }
.bgcolor-010 { background-color: #00ff00; }
.bgcolor-011 { background-color: #ffff00; }
.bgcolor-012 { background-color: #0000ff; }
.bgcolor-013 { background-color: #ff00ff; }
.bgcolor-014 { background-color: #00ffff; }
.bgcolor-015 { background-color: #ffffff; }
.bgcolor-016 { background-color: #000000; }
.bgcolor-017 { background-color: #00005f; }
.bgcolor-018 { background-color: #000087; }
.bgcolor-019 { background-color: #0000af; }
.bgcolor-020 { background-color: #0000df; }
.bgcolor-021 { background-color: #0000ff; }
.bgcolor-022 { background-color: #005f00; }
.bgcolor-023 { background-color: #005f5f; }
.bgcolor-024 { background-color: #005f87; }
.bgcolor-025 { background-color: #005faf; }
.bgcolor-026 { background-color: #005fdf; }
.bgcolor-027 { background-color: #005fff; }
.bgcolor-028 { background-color: #008700; }
.bgcolor-029 { background-color: #00875f; }
.bgcolor-030 { background-color: #008787; }
.bgcolor-031 { background-color: #0087af; }
.bgcolor-032 { background-color: #0087df; }
.bgcolor-033 { background-color: #0087ff; }
.bgcolor-034 { background-color: #00af00; }
.bgcolor-035 { background-color: #00af5f; }
.bgcolor-036 { background-color: #00af87; }
.bgcolor-037 { background-color: #00afaf; }
.bgcolor-038 { background-color: #00afdf; }
.bgcolor-039 { background-color: #00afff; }
.bgcolor-040 { background-color: #00df00; }
.bgcolor-041 { background-color: #00df5f; }
.bgcolor-042 { background-color: #00df87; }
.bgcolor-043 { background-color: #00dfaf; }
.bgcolor-044 { background-color: #00dfdf; }
.bgcolor-045 { background-color: #00dfff; }
.bgcolor-046 { background-color: #00ff00; }
.bgcolor-047 { background-color: #00ff5f; }
.bgcolor-048 { background-color: #00ff87; }
.bgcolor-049 { background-color: #00ffaf; }
.bgcolor-050 { background-color: #00ffdf; }
.bgcolor-051 { background-color: #00ffff; }
.bgcolor-052 { background-color: #5f0000; }
.bgcolor-053 { background-color: #5f005f; }
.bgcolor-054 { background-color: #5f0087; }
.bgcolor-055 { background-color: #5f00af; }
.bgcolor-056 { background-color: #5f00df; }
.bgcolor-057 { background-color: #5f00ff; }
.bgcolor-058 { background-color: #5f5f00; }
.bgcolor-059 { background-color: #5f5f5f; }
.bgcolor-060 { background-color: #5f5f87; }
.bgcolor-061 { background-color: #5f5faf; }
.bgcolor-062 { background-color: #5f5fdf; }
.bgcolor-063 { background-color: #5f5fff; }
.bgcolor-064 { background-color: #5f8700; }
.bgcolor-065 { background-color: #5f875f; }
.bgcolor-066 { background-color: #5f8787; }
.bgcolor-067 { background-color: #5f87af; }
.bgcolor-068 { background-color: #5f87df; }
.bgcolor-069 { background-color: #5f87ff; }
.bgcolor-070 { background-color: #5faf00; }
.bgcolor-071 { background-color: #5faf5f; }
.bgcolor-072 { background-color: #5faf87; }
.bgcolor-073 { background-color: #5fafaf; }
.bgcolor-074 { background-color: #5fafdf; }
.bgcolor-075 { background-color: #5fafff; }
.bgcolor-076 { background-color: #5fdf00; }
.bgcolor-077 { background-color: #5fdf5f; }
.bgcolor-078 { background-color: #5fdf87; }
.bgcolor-079 { background-color: #5fdfaf; }
.bgcolor-080 { background-color: #5fdfdf; }
.bgcolor-081 { background-color: #5fdfff; }
.bgcolor-082 { background-color: #5fff00; }
.bgcolor-083 { background-color: #5fff5f; }
.bgcolor-084 { background-color: #5fff87; }
.bgcolor-085 { background-color: #5fffaf; }
.bgcolor-086 { background-color: #5fffdf; }
.bgcolor-087 { background-color: #5fffff; }
.bgcolor-088 { background-color: #870000; }
.bgcolor-089 { background-color: #87005f; }
.bgcolor-090 { background-color: #870087; }
.bgcolor-091 { background-color: #8700af; }
.bgcolor-092 { background-color: #8700df; }
.bgcolor-093 { background-color: #8700ff; }
.bgcolor-094 { background-color: #875f00; }
.bgcolor-095 { background-color: #875f5f; }
.bgcolor-096 { background-color: #875f87; }
.bgcolor-097 { background-color: #875faf; }
.bgcolor-098 { background-color: #875fdf; }
.bgcolor-099 { background-color: #875fff; }
.bgcolor-100 { background-color: #878700; }
.bgcolor-101 { background-color: #87875f; }
.bgcolor-102 { background-color: #878787; }
.bgcolor-103 { background-color: #8787af; }
.bgcolor-104 { background-color: #8787df; }
.bgcolor-105 { background-color: #8787ff; }
.bgcolor-106 { background-color: #87af00; }
.bgcolor-107 { background-color: #87af5f; }
.bgcolor-108 { background-color: #87af87; }
.bgcolor-109 { background-color: #87afaf; }
.bgcolor-110 { background-color: #87afdf; }
.bgcolor-111 { background-color: #87afff; }
.bgcolor-112 { background-color: #87df00; }
.bgcolor-113 { background-color: #87df5f; }
.bgcolor-114 { background-color: #87df87; }
.bgcolor-115 { background-color: #87dfaf; }
.bgcolor-116 { background-color: #87dfdf; }
.bgcolor-117 { background-color: #87dfff; }
.bgcolor-118 { background-color: #87ff00; }
.bgcolor-119 { background-color: #87ff5f; }
.bgcolor-120 { background-color: #87ff87; }
.bgcolor-121 { background-color: #87ffaf; }
.bgcolor-122 { background-color: #87ffdf; }
.bgcolor-123 { background-color: #87ffff; }
.bgcolor-124 { background-color: #af0000; }
.bgcolor-125 { background-color: #af005f; }
.bgcolor-126 { background-color: #af0087; }
.bgcolor-127 { background-color: #af00af; }
.bgcolor-128 { background-color: #af00df; }
.bgcolor-129 { background-color: #af00ff; }
.bgcolor-130 { background-color: #af5f00; }
.bgcolor-131 { background-color: #af5f5f; }
.bgcolor-132 { background-color: #af5f87; }
.bgcolor-133 { background-color: #af5faf; }
.bgcolor-134 { background-color: #af5fdf; }
.bgcolor-135 { background-color: #af5fff; }
.bgcolor-136 { background-color: #af8700; }
.bgcolor-137 { background-color: #af875f; }
.bgcolor-138 { background-color: #af8787; }
.bgcolor-139 { background-color: #af87af; }
.bgcolor-140 { background-color: #af87df; }
.bgcolor-141 { background-color: #af87ff; }
.bgcolor-142 { background-color: #afaf00; }
.bgcolor-143 { background-color: #afaf5f; }
.bgcolor-144 { background-color: #afaf87; }
.bgcolor-145 { background-color: #afafaf; }
.bgcolor-146 { background-color: #afafdf; }
.bgcolor-147 { background-color: #afafff; }
.bgcolor-148 { background-color: #afdf00; }
.bgcolor-149 { background-color: #afdf5f; }
.bgcolor-150 { background-color: #afdf87; }
.bgcolor-151 { background-color: #afdfaf; }
.bgcolor-152 { background-color: #afdfdf; }
.bgcolor-153 { background-color: #afdfff; }
.bgcolor-154 { background-color: #afff00; }
.bgcolor-155 { background-color: #afff5f; }
.bgcolor-156 { background-color: #afff87; }
.bgcolor-157 { background-color: #afffaf; }
.bgcolor-158 { background-color: #afffdf; }
.bgcolor-159 { background-color: #afffff; }
.bgcolor-160 { background-color: #df0000; }
.bgcolor-161 { background-color: #df005f; }
.bgcolor-162 { background-color: #df0087; }
.bgcolor-163 { background-color: #df00af; }
.bgcolor-164 { background-color: #df00df; }
.bgcolor-165 { background-color: #df00ff; }
.bgcolor-166 { background-color: #df5f00; }
.bgcolor-167 { background-color: #df5f5f; }
.bgcolor-168 { background-color: #df5f87; }
.bgcolor-169 { background-color: #df5faf; }
.bgcolor-170 { background-color: #df5fdf; }
.bgcolor-171 { background-color: #df5fff; }
.bgcolor-172 { background-color: #df8700; }
.bgcolor-173 { background-color: #df875f; }
.bgcolor-174 { background-color: #df8787; }
.bgcolor-175 { background-color: #df87af; }
.bgcolor-176 { background-color: #df87df; }
.bgcolor-177 { background-color: #df87ff; }
.bgcolor-178 { background-color: #dfaf00; }
.bgcolor-179 { background-color: #dfaf5f; }
.bgcolor-180 { background-color: #dfaf87; }
.bgcolor-181 { background-color: #dfafaf; }
.bgcolor-182 { background-color: #dfafdf; }
.bgcolor-183 { background-color: #dfafff; }
.bgcolor-184 { background-color: #dfdf00; }
.bgcolor-185 { background-color: #dfdf5f; }
.bgcolor-186 { background-color: #dfdf87; }
.bgcolor-187 { background-color: #dfdfaf; }
.bgcolor-188 { background-color: #dfdfdf; }
.bgcolor-189 { background-color: #dfdfff; }
.bgcolor-190 { background-color: #dfff00; }
.bgcolor-191 { background-color: #dfff5f; }
.bgcolor-192 { background-color: #dfff87; }
.bgcolor-193 { background-color: #dfffaf; }
.bgcolor-194 { background-color: #dfffdf; }
.bgcolor-195 { background-color: #dfffff; }
.bgcolor-196 { background-color: #ff0000; }
.bgcolor-197 { background-color: #ff005f; }
.bgcolor-198 { background-color: #ff0087; }
.bgcolor-199 { background-color: #ff00af; }
.bgcolor-200 { background-color: #ff00df; }
.bgcolor-201 { background-color: #ff00ff; }
.bgcolor-202 { background-color: #ff5f00; }
.bgcolor-203 { background-color: #ff5f5f; }
.bgcolor-204 { background-color: #ff5f87; }
.bgcolor-205 { background-color: #ff5faf; }
.bgcolor-206 { background-color: #ff5fdf; }
.bgcolor-207 { background-color: #ff5fff; }
.bgcolor-208 { background-color: #ff8700; }
.bgcolor-209 { background-color: #ff875f; }
.bgcolor-210 { background-color: #ff8787; }
.bgcolor-211 { background-color: #ff87af; }
.bgcolor-212 { background-color: #ff87df; }
.bgcolor-213 { background-color: #ff87ff; }
.bgcolor-214 { background-color: #ffaf00; }
.bgcolor-215 { background-color: #ffaf5f; }
.bgcolor-216 { background-color: #ffaf87; }
.bgcolor-217 { background-color: #ffafaf; }
.bgcolor-218 { background-color: #ffafdf; }
.bgcolor-219 { background-color: #ffafff; }
.bgcolor-220 { background-color: #ffdf00; }
.bgcolor-221 { background-color: #ffdf5f; }
.bgcolor-222 { background-color: #ffdf87; }
.bgcolor-223 { background-color: #ffdfaf; }
.bgcolor-224 { background-color: #ffdfdf; }
.bgcolor-225 { background-color: #ffdfff; }
.bgcolor-226 { background-color: #ffff00; }
.bgcolor-227 { background-color: #ffff5f; }
.bgcolor-228 { background-color: #ffff87; }
.bgcolor-229 { background-color: #ffffaf; }
.bgcolor-230 { background-color: #ffffdf; }
.bgcolor-231 { background-color: #ffffff; }
.bgcolor-232 { background-color: #080808; }
.bgcolor-233 { background-color: #121212; }
.bgcolor-234 { background-color: #1c1c1c; }
.bgcolor-235 { background-color: #262626; }
.bgcolor-236 { background-color: #303030; }
.bgcolor-237 { background-color: #3a3a3a; }
.bgcolor-238 { background-color: #444444; }
.bgcolor-239 { background-color: #4e4e4e; }
.bgcolor-240 { background-color: #585858; }
.bgcolor-241 { background-color: #606060; }
.bgcolor-242 { background-color: #666666; }
.bgcolor-243 { background-color: #767676; }
.bgcolor-244 { background-color: #808080; }
.bgcolor-245 { background-color: #8a8a8a; }
.bgcolor-246 { background-color: #949494; }
.bgcolor-247 { background-color: #9e9e9e; }
.bgcolor-248 { background-color: #a8a8a8; }
.bgcolor-249 { background-color: #b2b2b2; }
.bgcolor-250 { background-color: #bcbcbc; }
.bgcolor-251 { background-color: #c6c6c6; }
.bgcolor-252 { background-color: #d0d0d0; }
.bgcolor-253 { background-color: #dadada; }
.bgcolor-254 { background-color: #e4e4e4; }
.bgcolor-255 { background-color: #eeeeee; }
