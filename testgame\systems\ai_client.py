#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智匠MindCraft AI客户端配置
基于OpenAI接口的AI服务调用
"""

import os
import sys
try:
    from openai import OpenAI
except ImportError:
    # 如果没有安装openai，使用模拟实现
    class OpenAI:
        def __init__(self, **kwargs):
            self.base_url = kwargs.get('base_url', '')
            self.api_key = kwargs.get('api_key', '')
            self.chat = self.Chat()
        
        class Chat:
            def __init__(self):
                self.completions = self.Completions()
            
            class Completions:
                def create(self, **kwargs):
                    import time
                    import random
                    time.sleep(0.05)  # 模拟网络延迟
                    
                    class Choice:
                        def __init__(self):
                            self.message = self.Message()
                        
                        class Message:
                            content = "天地灵气震荡，预示着重大事件即将发生。此子前途不可限量。"
                    
                    class Response:
                        def __init__(self):
                            self.choices = [Choice()]
                    
                    return Response()
from django.conf import settings
import json
import time
from typing import List, Dict, Any, Optional

# 确保Django设置已加载
try:
    from django.conf import settings
    if not settings.configured:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
        django.setup()
except ImportError:
    # 如果没有Django，使用模拟配置
    class MockSettings:
        configured = True
        MINDCRAFT_API_BASE = 'https://api.mindcraft.com.cn/v1'
        MINDCRAFT_API_KEY = 'MC-94D4CC750E92436FB3FA51C9F41D03A9'
        MINDCRAFT_MODEL = 'deepseek-r1-siliconflow'
        AI_SETTINGS = {
            'MAX_TOKENS': 4000,
            'TEMPERATURE': 0.7,
            'STREAM': False
        }
    settings = MockSettings()


class MindCraftAIClient:
    """智匠MindCraft AI客户端"""
    
    def __init__(self):
        """初始化AI客户端"""
        self.base_url = getattr(settings, 'MINDCRAFT_API_BASE', 'https://api.mindcraft.com.cn/v1')
        self.api_key = getattr(settings, 'MINDCRAFT_API_KEY', '')
        self.model = getattr(settings, 'MINDCRAFT_MODEL', 'deepseek-r1-siliconflow')
        
        if not self.api_key:
            raise ValueError("未配置MINDCRAFT_API_KEY")
        
        # 初始化OpenAI客户端
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key
        )
        
        # AI设置
        ai_settings = getattr(settings, 'AI_SETTINGS', {})
        self.max_tokens = ai_settings.get('MAX_TOKENS', 4000)
        self.temperature = ai_settings.get('TEMPERATURE', 0.7)
        self.stream = ai_settings.get('STREAM', False)
        
        print(f"✅ MindCraft AI客户端初始化成功")
        print(f"   模型: {self.model}")
        print(f"   API地址: {self.base_url}")
    
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表，格式: [{"role": "system/user/assistant", "content": "..."}]
            **kwargs: 额外参数
        
        Returns:
            AI生成的响应文本
        """
        try:
            # 合并参数
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": kwargs.get('temperature', self.temperature),
                "max_tokens": kwargs.get('max_tokens', self.max_tokens),
                "stream": kwargs.get('stream', self.stream),
            }
            
            # 发送请求
            response = self.client.chat.completions.create(**params)
            
            if self.stream:
                # 处理流式响应
                result = ""
                for chunk in response:
                    if chunk.choices[0].delta.content:
                        result += chunk.choices[0].delta.content
                return result
            else:
                # 处理普通响应
                return response.choices[0].message.content
        
        except Exception as e:
            print(f"❌ AI请求失败: {e}")
            return f"AI服务暂时不可用: {str(e)}"
    
    def ai_director_decision(self, event_data: Dict[str, Any]) -> str:
        """
        AI导演决策生成
        
        Args:
            event_data: 事件数据
        
        Returns:
            AI导演的决策和响应
        """
        system_prompt = """你是一个仙侠MUD游戏的AI导演，负责根据游戏事件生成剧情响应和决策。

你的职责：
1. 分析游戏事件的影响和意义
2. 生成适合的剧情发展建议
3. 保持仙侠世界观的一致性
4. 让游戏体验更加有趣和引人入胜

回应格式要求：
- 简洁明了，符合仙侠风格
- 可以包含场景描述、NPC反应、环境变化等
- 保持神秘感和代入感"""

        user_prompt = f"""游戏中发生了以下事件，请作为AI导演给出合适的剧情响应：

事件类型：{event_data.get('event_type', 'Unknown')}
事件内容：{event_data.get('description', '无描述')}
发生时间：{event_data.get('timestamp', '未知时间')}
相关角色：{event_data.get('character', '未知角色')}
事件重要性：{event_data.get('priority', 'NORMAL')}

请生成一个简短的剧情响应（50-200字），体现仙侠世界的氛围。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        return self.chat_completion(messages, temperature=0.8, max_tokens=500)
    
    def agent_response(self, agent_type: str, context: str, character_name: str = "") -> str:
        """
        AI Agent响应生成
        
        Args:
            agent_type: Agent类型（tiandao/diling/qiling）
            context: 触发上下文
            character_name: 角色名称
        
        Returns:
            Agent的个性化响应
        """
        personalities = {
            "tiandao": "你是天道意识，代表宇宙意志和命运规律。说话神秘深邃，经常给出暗示性的预言或指引。",
            "diling": "你是地灵意识，代表场所的历史和记忆。说话亲切自然，善于讲述这个地方的故事传说。",
            "qiling": "你是器灵意识，代表法宝或物品的灵性。说话个性鲜明，有时骄傲有时调皮。"
        }
        
        system_prompt = f"""{personalities.get(agent_type, "你是一个神秘的存在")}

你需要根据当前情况给出20-80字的简短回应，要：
1. 符合你的身份和性格
2. 与当前情况相关
3. 保持仙侠风格
4. 不要过于直白，要有一定的神秘感"""

        user_prompt = f"""当前情况：{context}
角色：{character_name or '某位修行者'}

请以{agent_type}的身份给出一个简短回应："""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        return self.chat_completion(messages, temperature=0.9, max_tokens=200)
    
    def test_connection(self) -> bool:
        """测试AI连接"""
        try:
            test_messages = [
                {"role": "system", "content": "你是一个测试助手，请简单回应。"},
                {"role": "user", "content": "你好，请回复'AI连接正常'来确认服务可用。"}
            ]
            
            response = self.chat_completion(test_messages, max_tokens=50)
            
            if response and "AI连接正常" in response:
                print("✅ AI连接测试成功")
                return True
            else:
                print(f"⚠️ AI连接测试部分成功，响应: {response}")
                return True  # 只要有响应就算成功
                
        except Exception as e:
            print(f"❌ AI连接测试失败: {e}")
            return False


# 全局AI客户端实例
_ai_client = None

def get_ai_client() -> MindCraftAIClient:
    """获取AI客户端单例"""
    global _ai_client
    if _ai_client is None:
        _ai_client = MindCraftAIClient()
    return _ai_client


if __name__ == "__main__":
    # 测试AI客户端
    print("🧪 测试MindCraft AI客户端...")
    
    client = get_ai_client()
    
    # 测试连接
    if client.test_connection():
        print("\n🎭 测试AI导演功能...")
        
        # 测试AI导演决策
        test_event = {
            "event_type": "CultivationBreakthroughEvent",
            "description": "玩家testuser成功突破到筑基期",
            "timestamp": "2025-06-29 14:30:00",
            "character": "testuser",
            "priority": "HIGH"
        }
        
        director_response = client.ai_director_decision(test_event)
        print(f"AI导演响应: {director_response}")
        
        print("\n🌟 测试AI Agent功能...")
        
        # 测试天道意识
        agent_response = client.agent_response(
            "tiandao", 
            "玩家在此地进行修炼，引起了天地灵气的波动", 
            "testuser"
        )
        print(f"天道意识: {agent_response}")
    else:
        print("❌ AI连接失败，请检查配置") 