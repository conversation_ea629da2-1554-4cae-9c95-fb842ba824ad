#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统性能监控脚本
"""

import time
import requests
import json
from datetime import datetime

def monitor_api_performance():
    """监控API性能"""
    base_url = "http://localhost:4001"
    
    print(f"🔍 AI导演系统性能监控")
    print(f"   开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 测试API端点
    endpoints = [
        "/api/ai-director/status/",
        "/api/ai-director/parse-outline/",
        "/api/ai-director/make-decision/"
    ]
    
    for endpoint in endpoints:
        url = base_url + endpoint
        
        try:
            start_time = time.time()
            
            if endpoint.endswith("status/"):
                response = requests.get(url, timeout=10)
            else:
                test_data = {"test": True}
                response = requests.post(url, json=test_data, timeout=10)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            print(f"📊 {endpoint}")
            print(f"   状态码: {response.status_code}")
            print(f"   响应时间: {response_time:.1f}ms")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   响应: {data.get('success', 'N/A')}")
                except:
                    print(f"   响应: 非JSON格式")
            
            print()
            
        except Exception as e:
            print(f"❌ {endpoint}: 请求失败 - {e}")
            print()

if __name__ == "__main__":
    monitor_api_performance()
