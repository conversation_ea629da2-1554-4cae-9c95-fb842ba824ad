"""
AI导演系统Web API
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json

try:
    from systems.ai_director import get_ai_director
    from systems.handlers.ai_director_handler import AIDirectorHandler
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False


@csrf_exempt
@require_http_methods(["POST"])
def parse_story_outline(request):
    """解析故事大纲API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        data = json.loads(request.body)
        outline_text = data.get('outline_text', '')
        
        if not outline_text:
            return JsonResponse({
                'success': False,
                'error': '请提供故事大纲文本'
            })
        
        # 获取AI导演
        director = get_ai_director()
        outline = director.analyze_story_outline(outline_text)
        
        return JsonResponse({
            'success': True,
            'outline_id': outline.outline_id,
            'title': outline.title,
            'theme': outline.theme,
            'main_conflict': outline.main_conflict,
            'key_characters': outline.key_characters,
            'plot_points': len(outline.major_plot_points),
            'phases': [phase.value for phase in outline.expected_phases]
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@csrf_exempt
@require_http_methods(["POST"])
def make_ai_decision(request):
    """生成AI决策API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        data = json.loads(request.body)
        
        # 获取AI导演
        director = get_ai_director()
        decision = director.make_decision(data)
        
        return JsonResponse({
            'success': True,
            'decision_id': decision.decision_id,
            'decision_type': decision.decision_type.value,
            'content': decision.content,
            'confidence': decision.confidence,
            'response_time': decision.response_time * 1000,  # 转换为毫秒
            'next_actions': decision.context.get('next_actions', [])
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@require_http_methods(["GET"])
def get_ai_stats(request):
    """获取AI性能统计API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        director = get_ai_director()
        stats = director.get_performance_stats()
        
        cache_total = stats['cache_hits'] + stats['cache_misses']
        cache_hit_rate = (stats['cache_hits'] / cache_total * 100) if cache_total > 0 else 0
        
        return JsonResponse({
            'success': True,
            'total_decisions': stats['total_decisions'],
            'avg_response_time': stats['average_response_time'] * 1000,
            'cache_hit_rate': cache_hit_rate,
            'cache_hits': stats['cache_hits'],
            'cache_misses': stats['cache_misses']
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@require_http_methods(["GET"])
def ai_director_status(request):
    """获取AI导演状态API"""
    if not AI_AVAILABLE:
        return JsonResponse({
            'success': False,
            'error': 'AI导演系统未启用'
        })
    
    try:
        # 这里可以添加更多状态信息
        return JsonResponse({
            'success': True,
            'status': 'active',
            'ai_available': True,
            'version': '1.0.0'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })