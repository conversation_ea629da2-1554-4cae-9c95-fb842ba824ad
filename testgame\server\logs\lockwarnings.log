
25-06-30 11:11:11+08 [-] <PERSON><PERSON><PERSON><PERSON> on admin(account 1): access type 'examine' changed from 'examine:perm(Admin)' to 'examine:perm(Admin)' 
LockHandler on admin(account 1): access type 'edit' changed from 'edit:perm(Admin)' to 'edit:perm(Admin)' 
LockHandler on admin(account 1): access type 'delete' changed from 'delete:perm(Admin)' to 'delete:perm(Admin)' 
LockHandler on admin(account 1): access type 'boot' changed from 'boot:perm(Admin)' to 'boot:perm(Admin)' 
LockHandler on admin(account 1): access type 'msg' changed from 'msg:all()' to 'msg:all()' 
LockHandler on admin(account 1): access type 'noidletimeout' changed from 'noidletimeout:perm(Builder) or perm(noidletimeout)' to 'noidletimeout:perm(Builder) or perm(noidletimeout)'
25-06-30 11:11:11+08 [-] <PERSON><PERSON><PERSON><PERSON> on admin(account 1): access type 'examine' changed from 'examine:perm(Admin)' to 'examine:perm(<PERSON><PERSON><PERSON>)' 
<PERSON><PERSON><PERSON><PERSON> on admin(account 1): access type 'edit' changed from 'edit:perm(Admin)' to 'edit:false()' 
LockHandler on admin(account 1): access type 'delete' changed from 'delete:perm(Admin)' to 'delete:false()' 
LockHandler on admin(account 1): access type 'boot' changed from 'boot:perm(Admin)' to 'boot:false()' 
LockHandler on admin(account 1): access type 'msg' changed from 'msg:all()' to 'msg:all()'
25-06-30 11:11:11+08 [-] LockHandler on admin: access type 'get' changed from 'get:all()' to 'get:false()' 
LockHandler on admin: access type 'call' changed from 'call:true()' to 'call:false()' 
LockHandler on admin: access type 'teleport' changed from 'teleport:true()' to 'teleport:perm(Admin)' 
LockHandler on admin: access type 'teleport_here' changed from 'teleport_here:true()' to 'teleport_here:perm(Admin)'
25-06-30 11:11:11+08 [-] LockHandler on admin: access type 'puppet' changed from 'puppet:pperm(Developer)' to 'puppet:pid(1) or id(admin) or perm(Developer) or pperm(Developer)' 
LockHandler on admin: access type 'delete' changed from 'delete:perm(Admin)' to 'delete:pid(1) or perm(Admin)' 
LockHandler on admin: access type 'edit' changed from 'edit:perm(Admin)' to 'edit:pid(1) or perm(Admin)'
25-06-30 11:11:11+08 [-] LockHandler on admin: access type 'puppet' changed from 'puppet:pid(1) or id(admin) or perm(Developer) or pperm(Developer)' to 'puppet:id(1) or pid(1) or perm(Developer) or pperm(Developer)' 
LockHandler on admin: access type 'delete' changed from 'delete:pid(1) or perm(Admin)' to 'delete:id(1) or perm(Admin)'
25-06-30 11:11:11+08 [-] LockHandler on admin: access type 'examine' changed from 'examine:perm(Builder)' to 'examine:perm(Developer)' 
LockHandler on admin: access type 'edit' changed from 'edit:pid(1) or perm(Admin)' to 'edit:false()' 
LockHandler on admin: access type 'delete' changed from 'delete:id(1) or perm(Admin)' to 'delete:false()' 
LockHandler on admin: access type 'puppet' changed from 'puppet:id(1) or pid(1) or perm(Developer) or pperm(Developer)' to 'puppet:false()'
25-06-30 11:11:11+08 [-] LockHandler on Limbo: access type 'get' changed from 'get:all()' to 'get:false()' 
LockHandler on Limbo: access type 'puppet' changed from 'puppet:pperm(Developer)' to 'puppet:false()' 
LockHandler on Limbo: access type 'teleport' changed from 'teleport:true()' to 'teleport:false()' 
LockHandler on Limbo: access type 'teleport_here' changed from 'teleport_here:true()' to 'teleport_here:true()'
25-06-30 11:11:11+08 [-] LockHandler on Channel 'MudInfo' (None): access type 'control' changed from 'control:perm(Admin)' to 'control:perm(Developer)' 
LockHandler on Channel 'MudInfo' (None): access type 'listen' changed from 'listen:all()' to 'listen:perm(Admin)' 
LockHandler on Channel 'MudInfo' (None): access type 'send' changed from 'send:all()' to 'send:false()'
25-06-30 11:11:11+08 [-] LockHandler on Channel 'Public' (None): access type 'control' changed from 'control:perm(Admin)' to 'control:perm(Admin)' 
LockHandler on Channel 'Public' (None): access type 'listen' changed from 'listen:all()' to 'listen:all()' 
LockHandler on Channel 'Public' (None): access type 'send' changed from 'send:all()' to 'send:all()'