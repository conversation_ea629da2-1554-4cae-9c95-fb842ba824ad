#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统集成测试
将AI导演系统集成到Evennia testgame中并进行测试
"""

import os
import sys
import shutil
import time
from pathlib import Path

class AIDirectorIntegrator:
    """AI导演系统集成器"""
    
    def __init__(self):
        self.source_dir = Path("xiuxian_mud_new")
        self.target_dir = Path("testgame")
        self.test_results = []
    
    def log_result(self, test_name, success, message=""):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%H:%M:%S")
        }
        self.test_results.append(result)
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
    
    def check_directories(self):
        """检查目录结构"""
        if not self.source_dir.exists():
            self.log_result("目录检查", False, f"源目录不存在: {self.source_dir}")
            return False
        
        if not self.target_dir.exists():
            self.log_result("目录检查", False, f"目标目录不存在: {self.target_dir}")
            return False
        
        self.log_result("目录检查", True, "源目录和目标目录都存在")
        return True
    
    def copy_ai_director_files(self):
        """复制AI导演相关文件"""
        try:
            # 创建目标目录结构
            target_systems = self.target_dir / "systems"
            target_web_api = self.target_dir / "web" / "api"
            
            target_systems.mkdir(exist_ok=True)
            target_web_api.mkdir(parents=True, exist_ok=True)
            
            # 复制核心系统文件
            files_to_copy = [
                ("systems/ai_director.py", "systems/ai_director.py"),
                ("systems/ai_client.py", "systems/ai_client.py"),
                ("web/api/ai_director_api.py", "web/api/ai_director_api.py"),
                ("web/api/urls.py", "web/api/urls.py")
            ]
            
            copied_files = []
            for source_file, target_file in files_to_copy:
                source_path = self.source_dir / source_file
                target_path = self.target_dir / target_file
                
                if source_path.exists():
                    # 确保目标目录存在
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source_path, target_path)
                    copied_files.append(target_file)
                else:
                    print(f"   警告: 源文件不存在 {source_path}")
            
            if copied_files:
                self.log_result("文件复制", True, f"成功复制 {len(copied_files)} 个文件")
                return True
            else:
                self.log_result("文件复制", False, "没有文件被复制")
                return False
                
        except Exception as e:
            self.log_result("文件复制", False, f"复制失败: {str(e)}")
            return False
    
    def update_settings(self):
        """更新settings.py配置"""
        try:
            settings_path = self.target_dir / "server" / "conf" / "settings.py"
            
            if not settings_path.exists():
                self.log_result("设置更新", False, "settings.py文件不存在")
                return False
            
            # 读取现有设置
            with open(settings_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加AI导演相关配置
            ai_config = '''

# AI导演系统配置
AI_SETTINGS = {
    'MAX_TOKENS': 4000,
    'TEMPERATURE': 0.7,
    'STREAM': False,
}

# MindCraft API配置 (如果需要)
MINDCRAFT_API_BASE = 'https://api.mindcraft.com.cn/v1'
MINDCRAFT_API_KEY = ''  # 请设置您的API密钥
MINDCRAFT_MODEL = 'deepseek-r1-siliconflow'
'''
            
            # 检查是否已经添加过配置
            if "AI_SETTINGS" not in content:
                with open(settings_path, 'a', encoding='utf-8') as f:
                    f.write(ai_config)
                self.log_result("设置更新", True, "成功添加AI导演配置")
            else:
                self.log_result("设置更新", True, "AI导演配置已存在")
            
            return True
            
        except Exception as e:
            self.log_result("设置更新", False, f"更新失败: {str(e)}")
            return False
    
    def update_urls(self):
        """更新URL配置"""
        try:
            urls_path = self.target_dir / "web" / "urls.py"
            
            if not urls_path.exists():
                self.log_result("URL更新", False, "web/urls.py文件不存在")
                return False
            
            # 读取现有URL配置
            with open(urls_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要添加API路由
            if 'path("api/"' not in content:
                # 在urlpatterns中添加API路由
                api_route = '    path("api/", include("web.api.urls")),\n'
                
                # 找到urlpatterns的位置并插入
                lines = content.split('\n')
                new_lines = []
                in_urlpatterns = False
                
                for line in lines:
                    new_lines.append(line)
                    if 'urlpatterns = [' in line:
                        in_urlpatterns = True
                    elif in_urlpatterns and line.strip().startswith('path('):
                        # 在第一个path后插入API路由
                        new_lines.append(api_route.rstrip())
                        in_urlpatterns = False
                
                # 写回文件
                with open(urls_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(new_lines))
                
                self.log_result("URL更新", True, "成功添加API路由")
            else:
                self.log_result("URL更新", True, "API路由已存在")
            
            return True
            
        except Exception as e:
            self.log_result("URL更新", False, f"更新失败: {str(e)}")
            return False
    
    def create_init_files(self):
        """创建必要的__init__.py文件"""
        try:
            init_files = [
                self.target_dir / "systems" / "__init__.py",
                self.target_dir / "web" / "api" / "__init__.py"
            ]
            
            created_files = []
            for init_file in init_files:
                if not init_file.exists():
                    init_file.touch()
                    created_files.append(str(init_file))
            
            if created_files:
                self.log_result("初始化文件", True, f"创建了 {len(created_files)} 个__init__.py文件")
            else:
                self.log_result("初始化文件", True, "所有__init__.py文件已存在")
            
            return True
            
        except Exception as e:
            self.log_result("初始化文件", False, f"创建失败: {str(e)}")
            return False
    
    def test_import(self):
        """测试模块导入"""
        try:
            # 添加testgame到Python路径
            testgame_path = str(self.target_dir.absolute())
            if testgame_path not in sys.path:
                sys.path.insert(0, testgame_path)
            
            # 尝试导入AI导演模块
            try:
                from systems.ai_director import AIDirector
                self.log_result("模块导入", True, "成功导入AIDirector")
                return True
            except ImportError as e:
                self.log_result("模块导入", False, f"导入失败: {str(e)}")
                return False
                
        except Exception as e:
            self.log_result("模块导入", False, f"测试异常: {str(e)}")
            return False
    
    def create_test_script(self):
        """创建测试脚本"""
        try:
            test_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统testgame集成测试脚本
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
django.setup()

def test_ai_director():
    """测试AI导演功能"""
    try:
        from systems.ai_director import AIDirector
        
        print("🎭 测试AI导演系统...")
        director = AIDirector()
        
        # 测试故事大纲解析
        test_outline = """
        《逆天改命》
        主题：凡人逆天修仙，挑战命运束缚
        核心冲突：废灵根与天道的对抗
        主要角色：林逸风、苏清雪、魔君血煞
        """
        
        print("📖 测试故事大纲解析...")
        outline = director.analyze_story_outline(test_outline)
        print(f"   标题: {outline.title}")
        print(f"   主题: {outline.theme}")
        
        # 测试AI决策
        print("🎮 测试AI决策生成...")
        event_data = {
            "event_type": "CultivationBreakthrough",
            "character": "testuser",
            "description": "玩家突破到筑基期"
        }
        
        decision = director.make_decision(event_data)
        print(f"   决策类型: {decision.decision_type.value}")
        print(f"   决策内容: {decision.content[:100]}...")
        print(f"   响应时间: {decision.response_time*1000:.1f}ms")
        
        print("✅ AI导演系统测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ AI导演系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ai_director()
'''
            
            test_script_path = self.target_dir / "test_ai_director.py"
            with open(test_script_path, 'w', encoding='utf-8') as f:
                f.write(test_script_content)
            
            self.log_result("测试脚本", True, f"创建测试脚本: {test_script_path}")
            return True
            
        except Exception as e:
            self.log_result("测试脚本", False, f"创建失败: {str(e)}")
            return False
    
    def run_integration(self):
        """运行完整集成流程"""
        print("🔧 开始AI导演系统集成")
        print(f"   源目录: {self.source_dir}")
        print(f"   目标目录: {self.target_dir}")
        print(f"   开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 执行集成步骤
        steps = [
            ("目录检查", self.check_directories),
            ("文件复制", self.copy_ai_director_files),
            ("设置更新", self.update_settings),
            ("URL更新", self.update_urls),
            ("初始化文件", self.create_init_files),
            ("模块导入测试", self.test_import),
            ("测试脚本创建", self.create_test_script)
        ]
        
        for step_name, step_func in steps:
            print(f"\n🔧 执行步骤: {step_name}")
            step_func()
        
        # 输出集成总结
        self.print_summary()
    
    def print_summary(self):
        """打印集成总结"""
        print("\n" + "=" * 60)
        print("集成总结")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        for result in self.test_results:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            print(f"{result['test']}: {status}")
            if result['message']:
                print(f"   {result['message']}")
        
        print(f"\n总计: {passed}/{total} 步骤成功")
        
        if passed == total:
            print("\n🎉 AI导演系统集成完成!")
            print("现在可以重启Evennia服务器并测试AI导演功能")
            print("\n下一步:")
            print("1. 停止当前Evennia服务器")
            print("2. 重启服务器: python -m evennia start")
            print("3. 运行测试: python test_ai_director.py")
        else:
            print(f"\n⚠️ 有 {total - passed} 个步骤失败")
            print("请检查错误信息并手动修复问题")


def main():
    """主函数"""
    integrator = AIDirectorIntegrator()
    integrator.run_integration()


if __name__ == "__main__":
    main()
