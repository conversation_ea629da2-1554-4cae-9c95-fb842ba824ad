#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演剧情规划引擎
基于LLMClient构建的智能剧情生成和管理系统
"""

import time
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import threading
from collections import deque

try:
    from .ai_client import get_ai_client
except ImportError:
    # 如果导入失败，创建一个模拟的AI客户端
    class MockAIClient:
        def chat_completion(self, messages, **kwargs):
            import time
            import random
            time.sleep(random.uniform(0.03, 0.08))
            
            # 根据消息内容返回不同的响应
            user_msg = messages[-1]["content"] if messages else ""
            
            if "解析" in user_msg and "故事大纲" in user_msg:
                return '''{"title": "逆天改命", "theme": "凡人逆天修仙", "main_conflict": "废灵根与天道对抗", "key_characters": ["林逸风", "苏清雪"], "major_plot_points": [{"phase": "序章", "description": "觉醒特殊体质"}], "expected_phases": ["序章", "起承", "高潮", "转合", "终章"]}'''
            else:
                return '''{"decision_type": "剧情推进", "content": "天地灵气震荡，此事必有深意。", "confidence": 0.85, "next_actions": ["调查原因", "加强防御"]}'''
    
    def get_ai_client():
        return MockAIClient()
from .event_system import XianxiaEventBus, BaseEvent

try:
    from evennia import logger
except ImportError:
    class SimpleLogger:
        def log_info(self, msg):
            print(f"[INFO] {msg}")
        def log_error(self, msg):
            print(f"[ERROR] {msg}")
        def log_warn(self, msg):
            print(f"[WARN] {msg}")
    logger = SimpleLogger()


class StoryPhase(Enum):
    """故事阶段"""
    PROLOGUE = "序章"
    RISING_ACTION = "起承"
    CLIMAX = "高潮"
    FALLING_ACTION = "转合"
    EPILOGUE = "终章"


class DecisionType(Enum):
    """AI决策类型"""
    PLOT_ADVANCEMENT = "剧情推进"
    CHARACTER_DEVELOPMENT = "角色发展"
    WORLD_EVENT = "世界事件"
    CONFLICT_RESOLUTION = "冲突解决"
    NARRATIVE_TWIST = "剧情转折"


@dataclass
class StoryOutline:
    """故事大纲结构"""
    outline_id: str
    title: str
    theme: str
    main_conflict: str
    key_characters: List[str]
    major_plot_points: List[Dict[str, Any]]
    expected_phases: List[StoryPhase]
    created_at: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StoryState:
    """剧情状态追踪"""
    current_phase: StoryPhase
    completed_plot_points: List[str]
    active_storylines: Dict[str, Dict[str, Any]]
    character_arcs: Dict[str, List[str]]
    world_changes: List[Dict[str, Any]]
    decision_history: deque
    last_update: float


@dataclass
class AIDecision:
    """AI决策结果"""
    decision_id: str
    decision_type: DecisionType
    content: str
    context: Dict[str, Any]
    confidence: float
    response_time: float
    timestamp: float


class AIDirector:
    """
    AI导演核心引擎
    负责智能剧情规划、故事大纲解析和决策生成
    """
    
    def __init__(self):
        self.llm_client = get_ai_client()
        self.event_bus = XianxiaEventBus.get_instance()
        
        # 故事管理
        self.story_outlines: Dict[str, StoryOutline] = {}
        self.story_states: Dict[str, StoryState] = {}
        self.active_outline_id: Optional[str] = None
        
        # 决策缓存和历史
        self.decision_cache: Dict[str, AIDecision] = {}
        self.decision_history = deque(maxlen=100)
        
        # 性能监控
        self.performance_stats = {
            "total_decisions": 0,
            "average_response_time": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        
        # 线程安全
        self.lock = threading.Lock()
        
        logger.log_info("AI导演引擎初始化完成")
    
    def analyze_story_outline(self, outline_text: str) -> StoryOutline:
        """
        AI智能解析故事大纲
        
        Args:
            outline_text: 故事大纲文本
            
        Returns:
            解析后的StoryOutline对象
        """
        start_time = time.time()
        
        # 构建解析提示
        system_prompt = """你是一个专业的故事分析助手，擅长解析仙侠故事大纲。

你需要从文本中提取以下信息：
1. 故事主题
2. 核心冲突
3. 关键角色
4. 主要剧情点
5. 故事阶段划分

请以JSON格式返回解析结果。"""

        user_prompt = f"""请解析以下故事大纲：

{outline_text}

返回格式：
{{
    "title": "故事标题",
    "theme": "故事主题",
    "main_conflict": "核心冲突描述",
    "key_characters": ["角色1", "角色2"],
    "major_plot_points": [
        {{"phase": "序章", "description": "剧情点描述", "triggers": ["触发条件"]}},
        ...
    ],
    "expected_phases": ["序章", "起承", "高潮", "转合", "终章"]
}}"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            # 调用AI进行解析
            response = self.llm_client.chat_completion(
                messages,
                temperature=0.3,  # 降低温度以获得更准确的解析
                max_tokens=2000
            )
            
            # 解析AI响应
            parsed_data = self._parse_ai_response(response)
            
            # 创建StoryOutline对象
            outline_id = self._generate_outline_id(outline_text)
            outline = StoryOutline(
                outline_id=outline_id,
                title=parsed_data.get("title", "未命名故事"),
                theme=parsed_data.get("theme", "修仙之道"),
                main_conflict=parsed_data.get("main_conflict", "未知冲突"),
                key_characters=parsed_data.get("key_characters", []),
                major_plot_points=parsed_data.get("major_plot_points", []),
                expected_phases=[StoryPhase(phase) for phase in parsed_data.get("expected_phases", ["序章"])],
                created_at=time.time(),
                metadata={"original_text": outline_text}
            )
            
            # 存储大纲
            with self.lock:
                self.story_outlines[outline_id] = outline
                self._initialize_story_state(outline_id)
            
            elapsed_time = time.time() - start_time
            logger.log_info(f"故事大纲解析完成: {outline.title} (耗时: {elapsed_time:.2f}秒)")
            
            return outline
            
        except Exception as e:
            logger.log_error(f"故事大纲解析失败: {e}")
            # 返回默认大纲
            return self._create_default_outline(outline_text)
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """解析AI响应中的JSON数据"""
        try:
            # 尝试直接解析JSON
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果失败，尝试提取JSON部分
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except:
                    pass
            
            # 返回默认结构
            return {
                "title": "未解析故事",
                "theme": "修仙",
                "main_conflict": "天道与人道的冲突",
                "key_characters": [],
                "major_plot_points": [],
                "expected_phases": ["序章"]
            }
    
    def _generate_outline_id(self, text: str) -> str:
        """生成大纲ID"""
        return hashlib.md5(f"{text}_{time.time()}".encode()).hexdigest()[:12]
    
    def _initialize_story_state(self, outline_id: str):
        """初始化故事状态"""
        self.story_states[outline_id] = StoryState(
            current_phase=StoryPhase.PROLOGUE,
            completed_plot_points=[],
            active_storylines={},
            character_arcs={},
            world_changes=[],
            decision_history=deque(maxlen=50),
            last_update=time.time()
        )
    
    def _create_default_outline(self, text: str) -> StoryOutline:
        """创建默认大纲（当AI解析失败时）"""
        outline_id = self._generate_outline_id(text)
        return StoryOutline(
            outline_id=outline_id,
            title="默认故事",
            theme="修仙之道",
            main_conflict="凡人逆天改命",
            key_characters=["主角"],
            major_plot_points=[
                {"phase": "序章", "description": "故事开始", "triggers": ["game_start"]}
            ],
            expected_phases=[StoryPhase.PROLOGUE],
            created_at=time.time()
        )
    
    def make_decision(self, event_data: Dict[str, Any]) -> AIDecision:
        """
        基于事件数据做出AI决策
        
        Args:
            event_data: 事件数据字典
            
        Returns:
            AI决策结果
        """
        start_time = time.time()
        
        # 生成决策缓存键
        cache_key = self._generate_cache_key(event_data)
        
        # 检查缓存
        with self.lock:
            if cache_key in self.decision_cache:
                self.performance_stats["cache_hits"] += 1
                cached_decision = self.decision_cache[cache_key]
                logger.log_info(f"使用缓存决策: {cached_decision.decision_id}")
                return cached_decision
            
            self.performance_stats["cache_misses"] += 1
        
        # 获取当前故事状态
        story_context = self._get_story_context()
        
        # 构建决策提示
        system_prompt = """你是一个仙侠游戏的AI导演，需要根据当前事件和故事进展做出智能决策。

决策原则：
1. 保持故事连贯性和趣味性
2. 根据玩家行为调整剧情走向
3. 在适当时机制造冲突和转折
4. 维护游戏世界的逻辑一致性

决策类型：
- 剧情推进：推动主线或支线剧情
- 角色发展：角色成长和关系变化
- 世界事件：影响游戏世界的大事件
- 冲突解决：处理各种冲突和矛盾
- 剧情转折：制造意外和惊喜"""

        user_prompt = f"""当前故事状态：
{json.dumps(story_context, ensure_ascii=False, indent=2)}

新发生的事件：
{json.dumps(event_data, ensure_ascii=False, indent=2)}

请分析事件影响并给出AI导演决策。返回格式：
{{
    "decision_type": "决策类型",
    "content": "具体决策内容和剧情描述",
    "confidence": 0.8,
    "next_actions": ["建议的后续行动"]
}}"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            # 调用AI生成决策
            response = self.llm_client.chat_completion(
                messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            # 解析决策
            decision_data = self._parse_ai_response(response)
            
            # 创建决策对象
            decision = AIDecision(
                decision_id=f"decision_{int(time.time()*1000)}",
                decision_type=self._parse_decision_type(decision_data.get("decision_type", "剧情推进")),
                content=decision_data.get("content", response),
                context={
                    "event_data": event_data,
                    "story_context": story_context,
                    "next_actions": decision_data.get("next_actions", [])
                },
                confidence=float(decision_data.get("confidence", 0.7)),
                response_time=time.time() - start_time,
                timestamp=time.time()
            )
            
            # 更新性能统计和缓存
            with self.lock:
                self.decision_cache[cache_key] = decision
                self.decision_history.append(decision)
                self._update_performance_stats(decision.response_time)
            
            # 更新故事状态
            self._update_story_state(decision)
            
            logger.log_info(f"AI决策完成: {decision.decision_type.value} (耗时: {decision.response_time:.3f}秒)")
            
            return decision
            
        except Exception as e:
            logger.log_error(f"AI决策生成失败: {e}")
            # 返回默认决策
            return self._create_fallback_decision(event_data, time.time() - start_time)
    
    def _generate_cache_key(self, event_data: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 只使用关键字段生成缓存键
        key_fields = ["event_type", "character", "location", "action"]
        key_data = {k: event_data.get(k) for k in key_fields if k in event_data}
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()
    
    def _get_story_context(self) -> Dict[str, Any]:
        """获取当前故事上下文"""
        if not self.active_outline_id or self.active_outline_id not in self.story_states:
            return {"status": "no_active_story"}
        
        state = self.story_states[self.active_outline_id]
        outline = self.story_outlines[self.active_outline_id]
        
        return {
            "current_phase": state.current_phase.value,
            "theme": outline.theme,
            "main_conflict": outline.main_conflict,
            "completed_plot_points": len(state.completed_plot_points),
            "active_storylines": list(state.active_storylines.keys()),
            "recent_decisions": [
                {"type": d.decision_type.value, "time": d.timestamp}
                for d in list(state.decision_history)[-5:]
            ]
        }
    
    def _parse_decision_type(self, type_str: str) -> DecisionType:
        """解析决策类型"""
        type_map = {
            "剧情推进": DecisionType.PLOT_ADVANCEMENT,
            "角色发展": DecisionType.CHARACTER_DEVELOPMENT,
            "世界事件": DecisionType.WORLD_EVENT,
            "冲突解决": DecisionType.CONFLICT_RESOLUTION,
            "剧情转折": DecisionType.NARRATIVE_TWIST
        }
        return type_map.get(type_str, DecisionType.PLOT_ADVANCEMENT)
    
    def _update_performance_stats(self, response_time: float):
        """更新性能统计"""
        self.performance_stats["total_decisions"] += 1
        total = self.performance_stats["total_decisions"]
        avg = self.performance_stats["average_response_time"]
        self.performance_stats["average_response_time"] = (avg * (total - 1) + response_time) / total
    
    def _update_story_state(self, decision: AIDecision):
        """更新故事状态"""
        if not self.active_outline_id:
            return
        
        state = self.story_states.get(self.active_outline_id)
        if state:
            state.decision_history.append(decision)
            state.last_update = time.time()
            
            # 根据决策类型更新状态
            if decision.decision_type == DecisionType.PLOT_ADVANCEMENT:
                # 可能推进故事阶段
                self._check_phase_advancement(state)
            elif decision.decision_type == DecisionType.WORLD_EVENT:
                # 记录世界变化
                state.world_changes.append({
                    "timestamp": decision.timestamp,
                    "change": decision.content[:100]
                })
    
    def _check_phase_advancement(self, state: StoryState):
        """检查是否需要推进故事阶段"""
        # 简化的阶段推进逻辑
        phase_order = [
            StoryPhase.PROLOGUE,
            StoryPhase.RISING_ACTION,
            StoryPhase.CLIMAX,
            StoryPhase.FALLING_ACTION,
            StoryPhase.EPILOGUE
        ]
        
        current_index = phase_order.index(state.current_phase)
        if current_index < len(phase_order) - 1:
            # 基于完成的剧情点数量决定是否推进
            if len(state.completed_plot_points) > (current_index + 1) * 3:
                state.current_phase = phase_order[current_index + 1]
                logger.log_info(f"故事阶段推进到: {state.current_phase.value}")
    
    def _create_fallback_decision(self, event_data: Dict[str, Any], response_time: float) -> AIDecision:
        """创建备用决策（当AI失败时）"""
        fallback_responses = {
            "combat": "战斗的余波在空气中回荡，似乎预示着更大的风波即将来临。",
            "cultivation": "天地灵气微微震动，仿佛在回应着修行者的努力。",
            "exploration": "这片区域隐藏着不为人知的秘密，等待着有缘人发现。",
            "social": "人与人之间的因果纠葛，往往会在意想不到的时刻显现。"
        }
        
        event_type = event_data.get("event_type", "").lower()
        content = next(
            (v for k, v in fallback_responses.items() if k in event_type),
            "世事如棋，一切皆有定数。"
        )
        
        return AIDecision(
            decision_id=f"fallback_{int(time.time()*1000)}",
            decision_type=DecisionType.PLOT_ADVANCEMENT,
            content=content,
            context={"event_data": event_data, "fallback": True},
            confidence=0.5,
            response_time=response_time,
            timestamp=time.time()
        )
    
    def get_story_state(self, outline_id: Optional[str] = None) -> Optional[StoryState]:
        """获取故事状态"""
        target_id = outline_id or self.active_outline_id
        if target_id:
            return self.story_states.get(target_id)
        return None
    
    def set_active_outline(self, outline_id: str) -> bool:
        """设置活跃的故事大纲"""
        if outline_id in self.story_outlines:
            self.active_outline_id = outline_id
            logger.log_info(f"激活故事大纲: {self.story_outlines[outline_id].title}")
            return True
        return False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self.lock:
            return self.performance_stats.copy()
    
    def clear_cache(self):
        """清空决策缓存"""
        with self.lock:
            self.decision_cache.clear()
            logger.log_info("决策缓存已清空")


# 全局AI导演实例
_ai_director = None

def get_ai_director() -> AIDirector:
    """获取AI导演单例"""
    global _ai_director
    if _ai_director is None:
        _ai_director = AIDirector()
    return _ai_director