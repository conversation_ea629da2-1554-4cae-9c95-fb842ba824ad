#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI导演系统testgame集成测试脚本
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_director_standalone():
    """独立测试AI导演功能（不依赖Django）"""
    try:
        print("🎭 测试AI导演系统（独立模式）...")
        
        # 模拟导入AI导演模块
        try:
            # 创建简化的AI导演测试
            from systems.ai_client import get_ai_client
            print("✅ 成功导入AI客户端")
            
            # 测试AI客户端
            client = get_ai_client()
            print(f"✅ AI客户端初始化成功: {type(client).__name__}")
            
            # 测试简单的AI调用
            messages = [
                {"role": "system", "content": "你是一个仙侠游戏的AI导演"},
                {"role": "user", "content": "解析这个故事大纲：《逆天改命》主题：凡人逆天修仙"}
            ]
            
            start_time = time.time()
            response = client.chat_completion(messages, max_tokens=500)
            end_time = time.time()
            
            print(f"✅ AI响应成功")
            print(f"   响应时间: {(end_time - start_time)*1000:.1f}ms")
            print(f"   响应内容: {response[:100]}...")
            
            return True
            
        except ImportError as e:
            print(f"❌ 模块导入失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ AI导演系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """测试API端点"""
    try:
        print("\n📡 测试API端点...")
        
        # 检查API文件是否存在
        api_file = os.path.join("web", "api", "ai_director_api.py")
        if os.path.exists(api_file):
            print(f"✅ API文件存在: {api_file}")
        else:
            print(f"❌ API文件不存在: {api_file}")
            return False
        
        # 检查URL配置
        urls_file = os.path.join("web", "urls.py")
        if os.path.exists(urls_file):
            with open(urls_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'api/' in content:
                    print("✅ URL配置包含API路由")
                else:
                    print("❌ URL配置缺少API路由")
                    return False
        else:
            print(f"❌ URL文件不存在: {urls_file}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    try:
        print("\n📁 检查文件结构...")
        
        required_files = [
            "systems/ai_director.py",
            "systems/ai_client.py",
            "systems/event_system.py",
            "web/api/ai_director_api.py",
            "web/api/urls.py"
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path):
                existing_files.append(file_path)
                print(f"✅ {file_path}")
            else:
                missing_files.append(file_path)
                print(f"❌ {file_path}")
        
        print(f"\n文件统计: {len(existing_files)}/{len(required_files)} 存在")
        
        if missing_files:
            print(f"缺失文件: {missing_files}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件结构检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 AI导演系统集成测试")
    print(f"   工作目录: {os.getcwd()}")
    print(f"   测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("文件结构检查", test_file_structure),
        ("API端点检查", test_api_endpoints),
        ("AI导演功能测试", test_ai_director_standalone)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过!")
        print("AI导演系统已成功集成到testgame中")
        print("\n建议下一步:")
        print("1. 重启Evennia服务器")
        print("2. 访问 http://localhost:4001/api/ai-director/status/ 测试API")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败")
        print("请检查错误信息并修复问题")

if __name__ == "__main__":
    main()
